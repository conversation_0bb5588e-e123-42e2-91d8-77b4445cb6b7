/**
 * Navigation and page routing management
 * Reason for Function: Handles sidebar navigation, page routing, and dynamic content loading
 * Task Performed: Manages sidebar state, loads page content, handles navigation events
 * Linking Information:
 *   - Internal Link: Loads content from src/renderer/pages/*.html files
 *   - Internal Link: Interacts with sidebar elements in src/renderer/index.html
 *   - Internal Link: Updates page title in header dynamically
 */

class NavigationManager {
  constructor() {
    this.currentPage = 'dashboard';
    this.sidebarCollapsed = false;
    this.pages = {
      dashboard: { title: 'Dashboard', file: 'dashboard.html' },
      tasks: { title: 'Tasks', file: 'tasks.html' },
      clients: { title: 'Clients', file: 'clients.html' },
      projects: { title: 'Projects', file: 'projects.html' },
      payments: { title: 'Payments', file: 'payments.html' },
      invoice: { title: 'Invoice', file: 'invoice.html' },
      profile: { title: 'Profile', file: 'profile.html' },
      settings: { title: 'Settings', file: 'settings.html' }
    };
    this.init();
  }

  /**
   * Initialize navigation manager
   * Reason for Function: Sets up event listeners and loads initial page content
   * Task Performed: Binds navigation events, sets up sidebar toggle, loads default page
   * Linking Information:
   *   - Internal Link: Called on page load to initialize navigation system
   */
  init() {
    this.bindEvents();
    this.loadPage(this.currentPage);
  }

  /**
   * Bind navigation events
   * Reason for Function: Attaches event listeners to navigation elements
   * Task Performed: Sets up sidebar toggle, navigation links, and logout functionality
   * Linking Information:
   *   - Internal Link: Binds to navigation elements in src/renderer/index.html
   */
  bindEvents() {
    // Sidebar toggle
    const sidebarToggle = document.getElementById('sidebar-toggle');
    if (sidebarToggle) {
      sidebarToggle.addEventListener('click', () => this.toggleSidebar());
    }

    // Navigation links
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
      item.addEventListener('click', (e) => {
        e.preventDefault();
        const page = item.getAttribute('data-page');
        if (page) {
          this.navigateToPage(page);
        }
      });
    });

    // Logout button
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
      logoutBtn.addEventListener('click', () => this.handleLogout());
    }

    // Handle keyboard shortcuts
    document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));

    // Profile dropdown functionality
    const profileBtn = document.getElementById('profile-btn');
    const profileDropdown = document.getElementById('profile-dropdown');
    const gotoProfile = document.getElementById('goto-profile');
    const dropdownLogout = document.getElementById('dropdown-logout');

    if (profileBtn && profileDropdown) {
      profileBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        this.toggleProfileDropdown();
      });

      // Close dropdown when clicking outside
      document.addEventListener('click', () => {
        this.closeProfileDropdown();
      });

      // Prevent dropdown from closing when clicking inside it
      profileDropdown.addEventListener('click', (e) => {
        e.stopPropagation();
      });
    }

    if (gotoProfile) {
      gotoProfile.addEventListener('click', () => {
        this.navigateToPage('profile');
        this.closeProfileDropdown();
      });
    }

    if (dropdownLogout) {
      dropdownLogout.addEventListener('click', () => {
        this.handleLogout();
        this.closeProfileDropdown();
      });
    }
  }

  /**
   * Toggle sidebar collapsed state
   * Reason for Function: Manages sidebar expansion/collapse with smooth animations
   * Task Performed: Toggles sidebar width and updates UI state
   * Linking Information:
   *   - Internal Link: Modifies sidebar classes defined in src/renderer/styles/dashboard.css
   */
  toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    if (sidebar) {
      this.sidebarCollapsed = !this.sidebarCollapsed;
      
      if (this.sidebarCollapsed) {
        sidebar.classList.add('collapsed');
      } else {
        sidebar.classList.remove('collapsed');
      }

      // Store preference in localStorage
      localStorage.setItem('sidebarCollapsed', this.sidebarCollapsed.toString());
    }
  }

  /**
   * Navigate to a specific page
   * Reason for Function: Handles page navigation with proper state management
   * Task Performed: Updates active navigation state, loads page content, updates title
   * Linking Information:
   *   - Internal Link: Calls loadPage method to load content from pages directory
   */
  navigateToPage(page) {
    if (this.pages[page] && page !== this.currentPage) {
      // Update navigation state
      this.updateActiveNavItem(page);
      
      // Load page content
      this.loadPage(page);
      
      // Update current page
      this.currentPage = page;
      
      // Update page title
      this.updatePageTitle(this.pages[page].title);
    }
  }

  /**
   * Load page content dynamically
   * Reason for Function: Loads HTML content for the specified page into the content area
   * Task Performed: Fetches page HTML file and injects it into the page content container
   * Linking Information:
   *   - Internal Link: Loads HTML files from src/renderer/pages/ directory
   */
  async loadPage(page) {
    const pageInfo = this.pages[page];
    if (!pageInfo) {
      console.error(`Page not found: ${page}`);
      return;
    }

    const pageContent = document.getElementById('page-content');
    if (!pageContent) {
      console.error('Page content container not found');
      return;
    }

    try {
      // Show loading state
      pageContent.innerHTML = '<div class="loading-spinner"></div>';
      
      // Fetch page content
      const response = await fetch(`pages/${pageInfo.file}`);
      if (!response.ok) {
        throw new Error(`Failed to load page: ${response.status}`);
      }
      
      const html = await response.text();
      pageContent.innerHTML = html;
      
      // Initialize page-specific functionality
      this.initializePageFeatures(page);
      
    } catch (error) {
      console.error('Error loading page:', error);
      pageContent.innerHTML = `
        <div class="error-container">
          <h3>Error Loading Page</h3>
          <p>Failed to load ${pageInfo.title}. Please try again.</p>
          <button class="btn btn-primary" onclick="window.navigationManager.loadPage('${page}')">
            Retry
          </button>
        </div>
      `;
    }
  }

  /**
   * Initialize page-specific features
   * Reason for Function: Sets up page-specific functionality after content is loaded
   * Task Performed: Initializes interactive elements and data loading for specific pages
   * Linking Information:
   *   - Internal Link: Called after page content is loaded to set up functionality
   */
  initializePageFeatures(page) {
    switch (page) {
      case 'dashboard':
        this.initializeDashboard();
        break;
      case 'tasks':
        this.initializeTasksPage();
        break;
      case 'clients':
        this.initializeClientsPage();
        break;
      case 'projects':
        this.initializeProjectsPage();
        break;
      case 'payments':
        this.initializePaymentsPage();
        break;
      case 'invoice':
        this.initializeInvoicePage();
        break;
      case 'profile':
        this.initializeProfilePage();
        break;
      // Add more page initializations as needed
    }
  }

  /**
   * Initialize dashboard features
   * Reason for Function: Sets up dashboard-specific functionality and data loading
   * Task Performed: Loads dashboard statistics and sets up quick action buttons
   * Linking Information:
   *   - Internal Link: Uses DashboardManager for data loading and statistics
   */
  async initializeDashboard() {
    try {
      // Initialize dashboard manager
      if (window.DashboardManager) {
        window.dashboardManager = new window.DashboardManager();
        console.log('Dashboard manager initialized');
      } else {
        console.error('DashboardManager not available');
        // Fallback to old method if DashboardManager is not available
        await this.loadDashboardStats();
      }

      // Set up quick action buttons
      const actionButtons = document.querySelectorAll('.action-btn');
      actionButtons.forEach(btn => {
        btn.addEventListener('click', () => {
          const action = btn.getAttribute('data-action');
          this.handleQuickAction(action);
        });
      });
    } catch (error) {
      console.error('Error initializing dashboard:', error);
    }
  }

  /**
   * Load dashboard statistics
   * Reason for Function: Fetches and displays dashboard statistics from the database
   * Task Performed: Retrieves task and project counts and updates dashboard display
   * Linking Information:
   *   - Internal Link: Uses SupabaseDB methods from supabase-client.js
   */
  async loadDashboardStats() {
    const authManager = window.getAuthManager();
    const currentUser = authManager?.getCurrentUser();
    
    if (!currentUser) return;

    try {
      // Get tasks and projects data
      const [tasksResult, projectsResult] = await Promise.all([
        window.SupabaseDB.getTasks(currentUser.id),
        window.SupabaseDB.getProjects(currentUser.id)
      ]);

      if (tasksResult.data) {
        const tasks = tasksResult.data;
        const totalTasks = tasks.length;
        const inProgressTasks = tasks.filter(task => task.status === 'in_progress').length;
        const completedTasks = tasks.filter(task => task.status === 'completed').length;

        // Update dashboard stats
        this.updateStatCard('total-tasks', totalTasks);
        this.updateStatCard('in-progress-tasks', inProgressTasks);
        this.updateStatCard('completed-tasks', completedTasks);
      }

      if (projectsResult.data) {
        const projects = projectsResult.data;
        this.updateStatCard('total-projects', projects.length);
      }
    } catch (error) {
      console.error('Error loading dashboard stats:', error);
    }
  }

  /**
   * Update stat card display
   * Reason for Function: Updates the numerical display in dashboard stat cards
   * Task Performed: Updates the text content of stat number elements
   * Linking Information:
   *   - Internal Link: Updates elements in dashboard.html
   */
  updateStatCard(elementId, value) {
    const element = document.getElementById(elementId);
    if (element) {
      element.textContent = value;
    }
  }

  /**
   * Handle quick action buttons
   * Reason for Function: Processes quick action button clicks on dashboard
   * Task Performed: Navigates to appropriate pages or shows modals for quick actions
   * Linking Information:
   *   - Internal Link: May navigate to other pages using navigateToPage method
   */
  handleQuickAction(action) {
    switch (action) {
      case 'new-task':
        this.navigateToPage('tasks');
        break;
      case 'new-project':
        this.navigateToPage('projects');
        break;
      case 'new-client':
        this.navigateToPage('clients');
        break;
      default:
        console.log(`Quick action: ${action}`);
    }
  }

  /**
   * Initialize tasks page
   * Reason for Function: Sets up tasks page functionality and task manager
   * Task Performed: Creates TaskManager instance and initializes task management features
   * Linking Information:
   *   - Internal Link: Called when tasks page is loaded
   *   - Internal Link: Creates TaskManager from src/renderer/js/task-manager.js
   */
  initializeTasksPage() {
    // Initialize task manager
    if (window.TaskManager) {
      window.taskManager = new window.TaskManager();
      console.log('Task manager initialized');
    } else {
      console.error('TaskManager not available');
    }
  }

  /**
   * Initialize clients page
   * Reason for Function: Sets up clients page functionality and client manager
   * Task Performed: Creates ClientManager instance and initializes client management features
   * Linking Information:
   *   - Internal Link: Called when clients page is loaded
   *   - Internal Link: Creates ClientManager from src/renderer/js/client-manager.js
   */
  initializeClientsPage() {
    // Initialize client manager
    if (window.ClientManager) {
      window.clientManager = new window.ClientManager();
      console.log('Client manager initialized');
    } else {
      console.error('ClientManager not available');
    }
  }

  /**
   * Initialize projects page
   * Reason for Function: Sets up projects page functionality and project manager
   * Task Performed: Creates ProjectManager instance and initializes project management features
   * Linking Information:
   *   - Internal Link: Called when projects page is loaded
   *   - Internal Link: Creates ProjectManager from src/renderer/js/project-manager.js
   */
  initializeProjectsPage() {
    // Initialize project manager
    if (window.ProjectManager) {
      window.projectManager = new window.ProjectManager();
      console.log('Project manager initialized');
    } else {
      console.error('ProjectManager not available');
    }
  }

  /**
   * Initialize payments page
   * Reason for Function: Sets up payments page functionality and payment manager
   * Task Performed: Creates PaymentManager instance and initializes payment management features
   * Linking Information:
   *   - Internal Link: Called when payments page is loaded
   *   - Internal Link: Creates PaymentManager from src/renderer/js/payment-manager.js
   */
  initializePaymentsPage() {
    console.log('🔄 Initializing new payment system...');

    // Check if the script loaded properly
    if (window.paymentManagerScriptLoaded) {
      console.log('✅ Payment manager script loaded successfully');
    } else {
      console.warn('⚠️ Payment manager script load status unknown');
    }

    // Function to attempt initialization
    const attemptInitialization = () => {
      // Try the initialization function first
      if (window.initializePaymentSystem) {
        console.log('🔄 Using initializePaymentSystem function...');
        const success = window.initializePaymentSystem();
        if (success) {
          console.log('✅ Payment system initialized via function');
          return true;
        } else {
          console.error('❌ Payment system initialization function failed');
        }
      }

      // Fallback: Check if PaymentSystemManager class is available
      if (window.PaymentSystemManager) {
        console.log('✅ PaymentSystemManager class found, trying direct instantiation...');

        // Check if instance already exists
        if (window.paymentSystem) {
          console.log('✅ Payment system already initialized');
          return true;
        } else {
          console.log('🔄 Creating PaymentSystemManager instance...');
          try {
            window.paymentSystem = new window.PaymentSystemManager();
            console.log('✅ Payment system initialized successfully');
            return true;
          } catch (error) {
            console.error('❌ Error creating PaymentSystemManager:', error);
            this.showPaymentPageError();
            return false;
          }
        }
      }

      return false;
    };

    // Try immediate initialization
    if (attemptInitialization()) {
      return;
    }

    console.error('❌ PaymentSystemManager not available');
    console.log('Available window properties:', Object.keys(window).filter(key => key.includes('payment') || key.includes('Payment')));

    // Progressive retry with multiple attempts
    let retryCount = 0;
    const maxRetries = 5;
    const retryInterval = 500; // Start with 500ms

    const retryInitialization = () => {
      retryCount++;
      console.log(`🔄 Retry attempt ${retryCount}/${maxRetries} for payment system initialization...`);

      if (attemptInitialization()) {
        console.log('✅ Payment system initialized after retry');
        return;
      }

      if (retryCount < maxRetries) {
        setTimeout(retryInitialization, retryInterval * retryCount); // Exponential backoff
      } else {
        console.error('❌ Payment system initialization failed after all retries');
        this.showPaymentPageError();
      }
    };

    // Start retry process
    setTimeout(retryInitialization, retryInterval);
  }

  /**
   * Show payment page error
   * Reason for Function: Displays error message when payment page fails to initialize
   * Task Performed: Shows user-friendly error message with retry option
   * Linking Information:
   *   - Internal Link: Called when PaymentManager fails to initialize
   */
  showPaymentPageError() {
    const pageContent = document.getElementById('page-content');
    if (pageContent) {
      pageContent.innerHTML = `
        <div class="error-container">
          <div class="error-icon">⚠️</div>
          <h3>Payment Page Error</h3>
          <p>There was an error loading the payment page. Please check the developer console for details.</p>
          <button class="btn btn-primary" onclick="location.reload()">Reload Page</button>
          <button class="btn btn-secondary" onclick="window.electronAPI?.openDevTools()">Open Developer Tools</button>
        </div>
      `;
    }
  }

  /**
   * Initialize invoice page
   * Reason for Function: Sets up invoice page functionality and invoice manager
   * Task Performed: Creates InvoiceManager instance and initializes invoice management features
   * Linking Information:
   *   - Internal Link: Called when invoice page is loaded
   *   - Internal Link: Creates InvoiceManager from src/renderer/js/invoice-manager.js
   */
  initializeInvoicePage() {
    // Initialize invoice manager
    if (window.InvoiceManager) {
      window.invoiceManager = new window.InvoiceManager();
      console.log('Invoice manager initialized');
    } else {
      console.error('InvoiceManager not available');
    }
  }

  /**
   * Initialize profile page
   * Reason for Function: Sets up profile page functionality and profile manager
   * Task Performed: Creates ProfileManager instance and initializes profile management features
   * Linking Information:
   *   - Internal Link: Called when profile page is loaded
   *   - Internal Link: Creates ProfileManager from src/renderer/js/profile-manager.js
   */
  initializeProfilePage() {
    // Initialize profile manager
    if (window.ProfileManager) {
      window.profileManager = new window.ProfileManager();
      console.log('Profile manager initialized');
    } else {
      console.error('ProfileManager not available');
    }
  }

  /**
   * Update active navigation item
   * Reason for Function: Updates the visual state of navigation items
   * Task Performed: Removes active class from current item and adds to new item
   * Linking Information:
   *   - Internal Link: Modifies navigation classes in sidebar
   */
  updateActiveNavItem(page) {
    // Remove active class from all nav items
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => item.classList.remove('active'));
    
    // Add active class to current page nav item
    const currentNavItem = document.querySelector(`[data-page="${page}"]`);
    if (currentNavItem) {
      currentNavItem.classList.add('active');
    }
  }

  /**
   * Update page title in header
   * Reason for Function: Updates the page title displayed in the header
   * Task Performed: Changes the text content of the page title element
   * Linking Information:
   *   - Internal Link: Updates title element in header
   */
  updatePageTitle(title) {
    const pageTitle = document.getElementById('page-title');
    if (pageTitle) {
      pageTitle.textContent = title;
    }
  }

  /**
   * Handle logout
   * Reason for Function: Initiates user logout process
   * Task Performed: Calls authentication manager logout method
   * Linking Information:
   *   - Internal Link: Uses AuthManager logout method
   */
  async handleLogout() {
    const authManager = window.getAuthManager();
    if (authManager) {
      await authManager.logout();
    }
  }

  /**
   * Handle keyboard shortcuts
   * Reason for Function: Provides keyboard navigation shortcuts
   * Task Performed: Processes keyboard shortcuts for navigation
   * Linking Information:
   *   - Internal Link: Triggers navigation methods
   */
  handleKeyboardShortcuts(event) {
    // Ctrl/Cmd + B to toggle sidebar
    if ((event.ctrlKey || event.metaKey) && event.key === 'b') {
      event.preventDefault();
      this.toggleSidebar();
    }
  }

  /**
   * Toggle profile dropdown
   * Reason for Function: Shows/hides the profile dropdown menu
   * Task Performed: Toggles the visibility of the profile dropdown
   * Linking Information:
   *   - Internal Link: Called by profile button click handler
   */
  toggleProfileDropdown() {
    const profileDropdown = document.getElementById('profile-dropdown');
    if (profileDropdown) {
      profileDropdown.classList.toggle('hidden');
    }
  }

  /**
   * Close profile dropdown
   * Reason for Function: Hides the profile dropdown menu
   * Task Performed: Adds hidden class to profile dropdown
   * Linking Information:
   *   - Internal Link: Called when clicking outside dropdown or after actions
   */
  closeProfileDropdown() {
    const profileDropdown = document.getElementById('profile-dropdown');
    if (profileDropdown) {
      profileDropdown.classList.add('hidden');
    }
  }

  /**
   * Restore sidebar state from localStorage
   * Reason for Function: Restores user's sidebar preference on app startup
   * Task Performed: Reads sidebar state from localStorage and applies it
   * Linking Information:
   *   - Internal Link: Called during initialization
   */
  restoreSidebarState() {
    const savedState = localStorage.getItem('sidebarCollapsed');
    if (savedState === 'true') {
      this.toggleSidebar();
    }
  }
}

// Initialize navigation manager when DOM is loaded
let navigationManager;
document.addEventListener('DOMContentLoaded', () => {
  navigationManager = new NavigationManager();
  navigationManager.restoreSidebarState();
});

// Export for use in other modules
window.NavigationManager = NavigationManager;
window.navigationManager = navigationManager;
